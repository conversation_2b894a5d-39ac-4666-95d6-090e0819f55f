#!/bin/bash
#
# ocs-resize-image-size - Modify the recorded size of a Clonezilla image
#
# Description:
#   This script modifies the recorded disk size in a Clonezilla image so that
#   it can be restored to a smaller drive. It updates the pt.parted files
#   which contain the original disk size information that Clonezilla uses
#   for size validation during restoration.
#
# Usage:
#   ocs-resize-image-size [OPTIONS] IMAGE_NAME NEW_SIZE_GB
#
# Parameters:
#   IMAGE_NAME    - Name of the Clonezilla image directory
#   NEW_SIZE_GB   - New disk size in GB (e.g., 120 for 120GB)
#
# Options:
#   -h, --help           Show this help message
#   -v, --verbose        Enable verbose output
#   -b, --backup         Create backup of original files before modification
#   -f, --force          Force modification without confirmation
#   --dry-run            Show what would be changed without making changes
#   --ocsroot DIR        Specify image root directory (default: /home/<USER>
#
# Examples:
#   # Modify image to appear as 120GB disk
#   ocs-resize-image-size my-image 120
#
#   # Dry run to see what would be changed
#   ocs-resize-image-size --dry-run my-image 120
#
#   # Create backup and modify with verbose output
#   ocs-resize-image-size -v -b my-image 120
#

# Default values
ocsroot="/home/<USER>"
verbose=false
backup=false
force=false
dry_run=false
show_info=false

# Colors for output
if [ -t 1 ]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    NC='\033[0m' # No Color
else
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    NC=''
fi

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS] IMAGE_NAME NEW_SIZE_GB

Modify the recorded size of a Clonezilla image so it can be restored to a smaller drive.

Parameters:
  IMAGE_NAME    Name of the Clonezilla image directory
  NEW_SIZE_GB   New disk size in GB (e.g., 120 for 120GB)

Options:
  -h, --help           Show this help message
  -v, --verbose        Enable verbose output
  -b, --backup         Create backup of original files before modification
  -f, --force          Force modification without confirmation
  -i, --info           Show current image information and exit
  --dry-run            Show what would be changed without making changes
  --ocsroot DIR        Specify image root directory (default: /home/<USER>

Examples:
  # Modify image to appear as 120GB disk
  $0 my-image 120

  # Dry run to see what would be changed
  $0 --dry-run my-image 120

  # Create backup and modify with verbose output
  $0 -v -b my-image 120

Note: This script modifies the pt.parted files that contain disk size information.
      Use with caution and always test the modified image before production use.

      When restoring a modified image, you may need to use the -icds option:
      ocs-sr -icds restoredisk IMAGE_NAME /dev/sdX

      The -icds option skips disk size checking during restoration.
EOF
}

# Function for verbose output
log_verbose() {
    if [ "$verbose" = true ]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# Function for error output
log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Function for warning output
log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function for success output
log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to validate input
validate_input() {
    local image_name="$1"
    local new_size_gb="$2"
    
    # Check if image directory exists
    if [ ! -d "$ocsroot/$image_name" ]; then
        log_error "Image directory '$ocsroot/$image_name' not found"
        return 1
    fi
    
    # Check if new size is a valid number
    if ! [[ "$new_size_gb" =~ ^[0-9]+$ ]]; then
        log_error "New size must be a positive integer (GB)"
        return 1
    fi
    
    # Check if new size is reasonable (between 1GB and 10TB)
    if [ "$new_size_gb" -lt 1 ] || [ "$new_size_gb" -gt 10000 ]; then
        log_error "New size must be between 1GB and 10000GB (10TB)"
        return 1
    fi
    
    return 0
}

# Function to find pt.parted files
find_pt_parted_files() {
    local image_dir="$1"
    find "$image_dir" -name "*-pt.parted" -o -name "*-pt.parted.compact"
}

# Function to show current image information
show_image_info() {
    local image_dir="$1"

    echo "Current Image Information:"
    echo "========================="
    echo "Image directory: $image_dir"

    # Show Info-img-size.txt if it exists
    if [ -f "$image_dir/Info-img-size.txt" ]; then
        echo "Image size info:"
        cat "$image_dir/Info-img-size.txt" | sed 's/^/  /'
    fi

    # Show disk information from pt.parted files
    local pt_files=$(find_pt_parted_files "$image_dir")
    if [ -n "$pt_files" ]; then
        echo "Disk size information:"
        echo "$pt_files" | while read pt_file; do
            if [ -n "$pt_file" ] && [ -f "$pt_file" ]; then
                local current_size_sectors=$(get_current_disk_size "$pt_file")
                if [ -n "$current_size_sectors" ]; then
                    local current_size_gb=$(sectors_to_gb "$current_size_sectors")
                    local disk_name=$(basename "$pt_file")
                    echo "  $disk_name: ${current_size_gb}GB (${current_size_sectors} sectors)"
                fi
            fi
        done
    fi

    # Show partition information
    if [ -f "$image_dir/parts" ]; then
        echo "Partitions in image:"
        cat "$image_dir/parts" | sed 's/^/  /'
    fi

    echo
}

# Function to extract current disk size from pt.parted file
get_current_disk_size() {
    local pt_file="$1"
    
    if [ ! -f "$pt_file" ]; then
        return 1
    fi
    
    # Extract disk size in sectors from line like: "Disk /dev/sda: 488397168s"
    local size_sectors=$(grep -E "^Disk /dev/" "$pt_file" | awk -F":" '{print $2}' | sed -r -e "s/s$//g" -e "s/^[[:space:]]*//g")
    
    if [ -n "$size_sectors" ]; then
        echo "$size_sectors"
        return 0
    fi
    
    return 1
}

# Function to convert GB to sectors
gb_to_sectors() {
    local gb="$1"
    # 1 GB = 1,000,000,000 bytes, 1 sector = 512 bytes
    # So 1 GB = 1,953,125 sectors (approximately)
    echo $((gb * 1953125))
}

# Function to convert sectors to GB
sectors_to_gb() {
    local sectors="$1"
    # Convert sectors to GB (rounded)
    echo $((sectors / 1953125))
}

# Function to create backup of file
backup_file() {
    local file="$1"
    local backup_file="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ "$backup" = true ]; then
        cp "$file" "$backup_file"
        log_verbose "Created backup: $backup_file"
    fi
}

# Function to modify pt.parted file
modify_pt_parted_file() {
    local pt_file="$1"
    local new_size_sectors="$2"

    log_verbose "Modifying $pt_file"

    # Get current size
    local current_size_sectors=$(get_current_disk_size "$pt_file")
    if [ -z "$current_size_sectors" ]; then
        log_error "Could not extract current disk size from $pt_file"
        return 1
    fi

    local current_size_gb=$(sectors_to_gb "$current_size_sectors")
    local new_size_gb=$(sectors_to_gb "$new_size_sectors")

    log_verbose "Current size: ${current_size_gb}GB (${current_size_sectors} sectors)"
    log_verbose "New size: ${new_size_gb}GB (${new_size_sectors} sectors)"

    # Check if new size is smaller than current size
    if [ "$new_size_sectors" -gt "$current_size_sectors" ]; then
        log_warning "New size (${new_size_gb}GB) is larger than current size (${current_size_gb}GB)"
        log_warning "This may not be necessary unless you're expanding to a larger drive"
    fi

    if [ "$dry_run" = true ]; then
        echo "Would modify $pt_file:"
        echo "  Current: ${current_size_gb}GB (${current_size_sectors} sectors)"
        echo "  New:     ${new_size_gb}GB (${new_size_sectors} sectors)"
        return 0
    fi

    # Create backup if requested
    backup_file "$pt_file"

    # Extract the device name from the pt.parted file
    local device_name=$(basename "$pt_file" | sed 's/-pt\.parted.*$//')

    # Modify the file - replace the disk size line
    # Handle both formats: "Disk /dev/sda: 488397168s" and variations
    sed -i "s/^Disk \/dev\/[^:]*: ${current_size_sectors}s$/Disk \/dev\/${device_name}: ${new_size_sectors}s/" "$pt_file"

    # Verify the change
    local new_current_size=$(get_current_disk_size "$pt_file")
    if [ "$new_current_size" = "$new_size_sectors" ]; then
        log_success "Successfully modified $pt_file"
        return 0
    else
        log_error "Failed to modify $pt_file correctly"
        log_error "Expected: $new_size_sectors, Got: $new_current_size"
        return 1
    fi
}

# Function to update Info-img-size.txt if it exists
update_info_img_size() {
    local image_dir="$1"
    local info_file="$image_dir/Info-img-size.txt"
    
    if [ -f "$info_file" ]; then
        log_verbose "Updating $info_file"
        
        if [ "$dry_run" = true ]; then
            echo "Would update $info_file with current image size"
            return 0
        fi
        
        # Create backup if requested
        backup_file "$info_file"
        
        # Regenerate the file with current image size
        echo "Image size (Bytes):" > "$info_file"
        du -hs "$image_dir" >> "$info_file"
        
        log_success "Updated $info_file"
    fi
}

# Main function
main() {
    local image_name="$1"
    local new_size_gb="$2"
    
    # Validate input
    if ! validate_input "$image_name" "$new_size_gb"; then
        exit 1
    fi
    
    local image_dir="$ocsroot/$image_name"
    local new_size_sectors=$(gb_to_sectors "$new_size_gb")
    
    log_verbose "Image directory: $image_dir"
    log_verbose "Target size: ${new_size_gb}GB (${new_size_sectors} sectors)"
    
    # Find all pt.parted files
    local pt_files=$(find_pt_parted_files "$image_dir")
    
    if [ -z "$pt_files" ]; then
        log_error "No pt.parted files found in $image_dir"
        exit 1
    fi
    
    log_verbose "Found pt.parted files:"
    echo "$pt_files" | while read file; do
        log_verbose "  $file"
    done

    # Show current image information
    if [ "$verbose" = true ] || [ "$dry_run" = true ]; then
        echo
        show_image_info "$image_dir"
    fi

    # Show summary and ask for confirmation
    if [ "$force" = false ] && [ "$dry_run" = false ]; then
        echo
        echo "Modification Summary:"
        echo "===================="
        echo "  Image: $image_name"
        echo "  Location: $image_dir"
        echo "  New size: ${new_size_gb}GB (${new_size_sectors} sectors)"
        echo "  Files to modify: $(echo "$pt_files" | wc -l)"
        echo
        echo "Current disk sizes:"
        echo "$pt_files" | while read pt_file; do
            if [ -n "$pt_file" ] && [ -f "$pt_file" ]; then
                local current_size_sectors=$(get_current_disk_size "$pt_file")
                if [ -n "$current_size_sectors" ]; then
                    local current_size_gb=$(sectors_to_gb "$current_size_sectors")
                    local disk_name=$(basename "$pt_file")
                    echo "  $disk_name: ${current_size_gb}GB → ${new_size_gb}GB"
                fi
            fi
        done
        echo
        echo -n "Proceed with modification? [y/N]: "
        read -r response
        case "$response" in
            [yY]|[yY][eE][sS])
                ;;
            *)
                echo "Operation cancelled."
                exit 0
                ;;
        esac
    fi
    
    # Process each pt.parted file
    local success_count=0
    local total_count=0
    
    echo "$pt_files" | while read pt_file; do
        if [ -n "$pt_file" ]; then
            total_count=$((total_count + 1))
            if modify_pt_parted_file "$pt_file" "$new_size_sectors"; then
                success_count=$((success_count + 1))
            fi
        fi
    done
    
    # Update Info-img-size.txt
    update_info_img_size "$image_dir"
    
    if [ "$dry_run" = true ]; then
        echo
        log_success "Dry run completed. No files were modified."
    else
        echo
        log_success "Image size modification completed."
        log_warning "Remember to use the -icds option when restoring to skip disk size checking:"
        echo "  Example: ocs-sr -icds restoredisk $image_name /dev/sdX"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -v|--verbose)
            verbose=true
            shift
            ;;
        -b|--backup)
            backup=true
            shift
            ;;
        -f|--force)
            force=true
            shift
            ;;
        -i|--info)
            show_info=true
            shift
            ;;
        --dry-run)
            dry_run=true
            shift
            ;;
        --ocsroot)
            ocsroot="$2"
            shift 2
            ;;
        -*)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
        *)
            break
            ;;
    esac
done

# Handle info option
if [ "$show_info" = true ]; then
    if [ $# -ne 1 ]; then
        log_error "Info option requires exactly one argument (IMAGE_NAME)"
        usage
        exit 1
    fi

    image_name="$1"
    if [ ! -d "$ocsroot/$image_name" ]; then
        log_error "Image directory '$ocsroot/$image_name' not found"
        exit 1
    fi

    show_image_info "$ocsroot/$image_name"
    exit 0
fi

# Check if we have the required arguments for modification
if [ $# -ne 2 ]; then
    log_error "Missing required arguments"
    usage
    exit 1
fi

# Run main function
main "$1" "$2"
